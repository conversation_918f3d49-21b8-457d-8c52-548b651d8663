import sys
import pandas as pd
from PyQt6.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
    QLineEdit, QFileDialog, QComboBox, QListWidget, QListWidgetItem,
    QProgressBar, QTextEdit, QMessageBox, QTabWidget
)
from PyQt6.QtCore import QThread, pyqtSignal, Qt

class ComparisonWorker(QThread):
    """
    在单独的线程中运行比较，以防止UI冻结
    """
    progress = pyqtSignal(int)
    status = pyqtSignal(str)
    finished = pyqtSignal(object)

    def __init__(self, file1, file2, key_column, unique1_cols, unique2_cols):
        super().__init__()
        self.file1 = file1
        self.file2 = file2
        self.key_column = key_column
        self.unique1_cols = unique1_cols
        self.unique2_cols = unique2_cols

    def run(self):
        try:
            self.status.emit("开始读取文件...")
            self.progress.emit(10)
            
            df1 = pd.read_excel(self.file1, dtype={self.key_column: str})
            self.progress.emit(25)
            df2 = pd.read_excel(self.file2, dtype={self.key_column: str})
            self.progress.emit(40)

            self.status.emit("文件读取完毕，开始数据比对...")
            df1[self.key_column] = df1[self.key_column].astype(str).str.strip()
            df2[self.key_column] = df2[self.key_column].astype(str).str.strip()

            keys1 = set(df1[self.key_column].dropna())
            keys2 = set(df2[self.key_column].dropna())
            self.progress.emit(60)

            duplicates = list(keys1.intersection(keys2))
            unique_to_1 = df1[~df1[self.key_column].isin(keys2)]
            unique_to_2 = df2[~df2[self.key_column].isin(keys1)]
            self.progress.emit(80)

            self.status.emit("比对完成，正在准备结果...")
            result = {
                "duplicates": pd.DataFrame(duplicates, columns=[self.key_column]),
                "unique1": unique_to_1[self.unique1_cols],
                "unique2": unique_to_2[self.unique2_cols],
            }
            self.progress.emit(100)
            self.finished.emit(result)

        except Exception as e:
            self.status.emit(f"发生错误: {e}")
            self.finished.emit(None)

class DataValidationWorker(QThread):
    progress = pyqtSignal(int)
    status = pyqtSignal(str)
    finished = pyqtSignal(object)

    def __init__(self, file1, file2, key_column, cols1, cols2):
        super().__init__()
        self.file1 = file1
        self.file2 = file2
        self.key_column = key_column
        self.cols1 = cols1
        self.cols2 = cols2

    def run(self):
        try:
            self.status.emit("开始数据校验...")
            self.progress.emit(10)
            self.status.emit("正在读取文件...")
            df1 = pd.read_excel(self.file1, dtype={self.key_column: str})
            self.progress.emit(25)
            df2 = pd.read_excel(self.file2, dtype={self.key_column: str})
            self.progress.emit(40)
            df1.dropna(subset=[self.key_column], inplace=True)
            df2.dropna(subset=[self.key_column], inplace=True)
            df1[self.key_column] = df1[self.key_column].astype(str).str.strip()
            df2[self.key_column] = df2[self.key_column].astype(str).str.strip()
            self.status.emit("正在合并数据...")
            # 只保留主键和用户选择的列
            df1_sel = df1[[self.key_column] + self.cols1].copy()
            df2_sel = df2[[self.key_column] + self.cols2].copy()
            # 重命名为统一列名
            rename1 = {col: f'col{i+1}_A' for i, col in enumerate(self.cols1)}
            rename2 = {col: f'col{i+1}_B' for i, col in enumerate(self.cols2)}
            df1_sel = df1_sel.rename(columns=rename1)
            df2_sel = df2_sel.rename(columns=rename2)
            merged_df = pd.merge(df1_sel, df2_sel, on=self.key_column, how='inner')
            self.progress.emit(60)
            all_discrepancies = []
            for i in range(len(self.cols1)):
                colA = f'col{i+1}_A'
                colB = f'col{i+1}_B'
                diff = merged_df[merged_df[colA].fillna('').astype(str) != merged_df[colB].fillna('').astype(str)].copy()
                if not diff.empty:
                    diff['A表列名'] = self.cols1[i]
                    diff['B表列名'] = self.cols2[i]
                    diff['A表值'] = diff[colA]
                    diff['B表值'] = diff[colB]
                    all_discrepancies.append(diff[[self.key_column, 'A表列名', 'B表列名', 'A表值', 'B表值']])
            self.progress.emit(90)
            if not all_discrepancies:
                final_report = pd.DataFrame()
            else:
                final_report = pd.concat(all_discrepancies, ignore_index=True)
            self.progress.emit(100)
            self.finished.emit(final_report)
        except Exception as e:
            self.status.emit(f"发生错误: {e}")
            self.finished.emit(None)

class MainWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Excel 对比工具 v2.0")
        self.setGeometry(100, 100, 800, 700)
        # 初始化异常列表
        self.exception_list = []
        self.initUI()

    def initUI(self):
        main_layout = QVBoxLayout()

        # --- File Selection ---
        file_layout = QHBoxLayout()
        self.file1_path = QLineEdit(self)
        self.file1_path.setPlaceholderText("请选择文件1 (如：总表)")
        btn_file1 = QPushButton("浏览...")
        btn_file1.clicked.connect(lambda: self.select_file(self.file1_path))
        file_layout.addWidget(QLabel("文件1:"))
        file_layout.addWidget(self.file1_path)
        file_layout.addWidget(btn_file1)
        
        self.file2_path = QLineEdit(self)
        self.file2_path.setPlaceholderText("请选择文件2 (如：对比表)")
        btn_file2 = QPushButton("浏览...")
        btn_file2.clicked.connect(lambda: self.select_file(self.file2_path))
        file_layout.addWidget(QLabel("文件2:"))
        file_layout.addWidget(self.file2_path)
        file_layout.addWidget(btn_file2)
        
        # 新增一键清除按钮
        btn_reset = QPushButton("一键清除")
        btn_reset.clicked.connect(self.reset_all)
        file_layout.addWidget(btn_reset)
        main_layout.addLayout(file_layout)

        # --- Tabs for different functions ---
        tabs = QTabWidget()
        self.setup_diff_tab()
        self.setup_validation_tab()
        self.setup_extract_tab()
        self.setup_merge_tab()
        self.setup_column_to_txt_tab()
        tabs.addTab(self.diff_tab, "对比文档整体差异")
        tabs.addTab(self.validation_tab, "对比共同行数据差异")
        tabs.addTab(self.extract_tab, "提取指定行")
        tabs.addTab(self.merge_tab, "多表格合并")
        tabs.addTab(self.column_to_txt_tab, "指定列转txt文本")
        main_layout.addWidget(tabs)

        # --- 异常列表管理区域 ---
        exception_layout = QVBoxLayout()
        exception_header = QHBoxLayout()
        exception_header.addWidget(QLabel("异常列表管理:"))

        # 异常列表按钮
        self.view_exceptions_btn = QPushButton("查看异常列表")
        self.view_exceptions_btn.clicked.connect(self.view_exception_list)
        self.export_exceptions_btn = QPushButton("导出异常列表")
        self.export_exceptions_btn.clicked.connect(self.export_exception_list)
        self.clear_exceptions_btn = QPushButton("清空异常列表")
        self.clear_exceptions_btn.clicked.connect(self.clear_exception_list)

        exception_header.addWidget(self.view_exceptions_btn)
        exception_header.addWidget(self.export_exceptions_btn)
        exception_header.addWidget(self.clear_exceptions_btn)
        exception_header.addStretch()

        # 异常计数显示
        self.exception_count_label = QLabel("当前异常数量: 0")
        exception_header.addWidget(self.exception_count_label)

        exception_layout.addLayout(exception_header)
        main_layout.addLayout(exception_layout)

        # --- Progress and Logging ---
        self.progress_bar = QProgressBar(self)
        self.status_log = QTextEdit(self)
        self.status_log.setReadOnly(True)
        main_layout.addWidget(self.progress_bar)
        main_layout.addWidget(QLabel("日志:"))
        main_layout.addWidget(self.status_log)

        self.setLayout(main_layout)

    def setup_diff_tab(self):
        self.diff_tab = QWidget()
        layout = QVBoxLayout(self.diff_tab)
        
        # Add a key selection dropdown for this tab
        key_layout = QHBoxLayout()
        key_layout.addWidget(QLabel("对比基准列(两表通用):"))
        self.diff_key_combo = QComboBox(self.diff_tab)
        key_layout.addWidget(self.diff_key_combo)
        layout.addLayout(key_layout)

        # Existing layout for column selection
        cols_layout = QHBoxLayout()
        g1_layout = QVBoxLayout()
        g1_layout.addWidget(QLabel("文件1独有数据，选择输出列:"))
        self.unique1_cols_list = QListWidget(self)
        self.unique1_cols_list.setSelectionMode(QListWidget.SelectionMode.MultiSelection)
        g1_layout.addWidget(self.unique1_cols_list)
        
        g2_layout = QVBoxLayout()
        g2_layout.addWidget(QLabel("文件2独有数据，选择输出列:"))
        self.unique2_cols_list = QListWidget(self)
        self.unique2_cols_list.setSelectionMode(QListWidget.SelectionMode.MultiSelection)
        g2_layout.addWidget(self.unique2_cols_list)
        
        cols_layout.addLayout(g1_layout)
        cols_layout.addLayout(g2_layout)
        layout.addLayout(cols_layout)

        # Run button
        self.run_diff_button = QPushButton("开始对比整体差异", self)
        self.run_diff_button.clicked.connect(self.run_diff_comparison)
        layout.addWidget(self.run_diff_button)

    def setup_validation_tab(self):
        self.validation_tab = QWidget()
        layout = QVBoxLayout(self.validation_tab)

        # 主键选择区
        key_layout = QHBoxLayout()
        key_layout.addWidget(QLabel("行数据差异基准:"))
        self.key_column_combo = QComboBox(self.validation_tab)
        self.key_column_combo.currentTextChanged.connect(self.update_sub_lists)
        key_layout.addWidget(self.key_column_combo)
        layout.addLayout(key_layout)

        cols_layout = QHBoxLayout()
        v1_layout = QVBoxLayout()
        v1_layout.addWidget(QLabel("文件1选择对比列 (顺序对应):"))
        self.validate_cols_list1 = QListWidget(self.validation_tab)
        self.validate_cols_list1.setSelectionMode(QListWidget.SelectionMode.MultiSelection)
        v1_layout.addWidget(self.validate_cols_list1)
        v2_layout = QVBoxLayout()
        v2_layout.addWidget(QLabel("文件2选择对比列 (顺序对应):"))
        self.validate_cols_list2 = QListWidget(self.validation_tab)
        self.validate_cols_list2.setSelectionMode(QListWidget.SelectionMode.MultiSelection)
        v2_layout.addWidget(self.validate_cols_list2)
        run_layout = QVBoxLayout()
        self.run_validation_button = QPushButton("开始对比数据差异", self.validation_tab)
        self.run_validation_button.clicked.connect(self.run_validation_comparison)
        run_layout.addWidget(self.run_validation_button)
        cols_layout.addLayout(v1_layout)
        cols_layout.addLayout(v2_layout)
        cols_layout.addLayout(run_layout)
        layout.addLayout(cols_layout)

    def setup_extract_tab(self):
        self.extract_tab = QWidget()
        layout = QVBoxLayout(self.extract_tab)

        # 基准列选择
        key_layout = QHBoxLayout()
        key_layout.addWidget(QLabel("基准列:"))
        self.extract_key_combo = QComboBox(self.extract_tab)
        key_layout.addWidget(self.extract_key_combo)
        # 基准值来源
        key_layout.addWidget(QLabel("基准值来源:"))
        self.extract_base_file_combo = QComboBox(self.extract_tab)
        self.extract_base_file_combo.addItems(["文件1", "文件2"])
        self.extract_base_file_combo.currentTextChanged.connect(self.update_extract_key_combo)
        key_layout.addWidget(self.extract_base_file_combo)
        layout.addLayout(key_layout)

        # 提取目标文件选择
        target_layout = QHBoxLayout()
        target_layout.addWidget(QLabel("提取目标文件:"))
        self.extract_target_file_combo = QComboBox(self.extract_tab)
        self.extract_target_file_combo.addItems(["文件1", "文件2"])
        self.extract_target_file_combo.currentTextChanged.connect(self.update_extract_cols_list)
        target_layout.addWidget(self.extract_target_file_combo)
        layout.addLayout(target_layout)

        # 提取列多选
        cols_layout = QHBoxLayout()
        cols_layout.addWidget(QLabel("选择要提取的列:"))
        self.extract_cols_list = QListWidget(self.extract_tab)
        self.extract_cols_list.setSelectionMode(QListWidget.SelectionMode.MultiSelection)
        cols_layout.addWidget(self.extract_cols_list)
        layout.addLayout(cols_layout)

        # 按钮
        btn_layout = QHBoxLayout()
        self.run_extract_button = QPushButton("开始提取", self.extract_tab)
        self.run_extract_button.clicked.connect(self.run_extract)
        btn_layout.addWidget(self.run_extract_button)
        layout.addLayout(btn_layout)

    def setup_merge_tab(self):
        self.merge_tab = QWidget()
        layout = QVBoxLayout(self.merge_tab)
        # 文件多选
        file_layout = QHBoxLayout()
        self.merge_files_edit = QLineEdit(self.merge_tab)
        self.merge_files_edit.setPlaceholderText("请选择要合并的多个Excel文件（用;分隔）")
        self.merge_files_edit.setReadOnly(True)
        btn_select_files = QPushButton("选择文件", self.merge_tab)
        btn_select_files.clicked.connect(self.select_merge_files)
        file_layout.addWidget(self.merge_files_edit)
        file_layout.addWidget(btn_select_files)
        layout.addLayout(file_layout)
        # 合并按钮
        btn_merge = QPushButton("开始合并", self.merge_tab)
        btn_merge.clicked.connect(self.run_merge)
        layout.addWidget(btn_merge)

    def setup_column_to_txt_tab(self):
        self.column_to_txt_tab = QWidget()
        layout = QVBoxLayout(self.column_to_txt_tab)

        # 文件选择区域
        file_layout = QHBoxLayout()
        file_layout.addWidget(QLabel("选择文件:"))
        self.txt_file_combo = QComboBox(self.column_to_txt_tab)
        self.txt_file_combo.addItems(["文件1", "文件2"])
        self.txt_file_combo.currentTextChanged.connect(self.update_txt_columns)
        file_layout.addWidget(self.txt_file_combo)
        layout.addLayout(file_layout)

        # 列选择区域
        cols_layout = QHBoxLayout()

        # 第一列选择
        col1_layout = QVBoxLayout()
        col1_layout.addWidget(QLabel("选择第一列:"))
        self.txt_col1_combo = QComboBox(self.column_to_txt_tab)
        col1_layout.addWidget(self.txt_col1_combo)
        cols_layout.addLayout(col1_layout)

        # 第二列选择
        col2_layout = QVBoxLayout()
        col2_layout.addWidget(QLabel("选择第二列:"))
        self.txt_col2_combo = QComboBox(self.column_to_txt_tab)
        col2_layout.addWidget(self.txt_col2_combo)
        cols_layout.addLayout(col2_layout)

        layout.addLayout(cols_layout)

        # 按钮
        btn_layout = QHBoxLayout()
        self.run_txt_button = QPushButton("生成txt文件", self.column_to_txt_tab)
        self.run_txt_button.clicked.connect(self.run_column_to_txt)
        btn_layout.addWidget(self.run_txt_button)
        layout.addLayout(btn_layout)

    def select_file(self, line_edit):
        path, _ = QFileDialog.getOpenFileName(self, "选择Excel文件", "", "Excel Files (*.xlsx *.xls)")
        if path:
            line_edit.setText(path)
            self.update_column_selections()

    def update_column_selections(self):
        path1 = self.file1_path.text()
        path2 = self.file2_path.text()
        if not (path1 and path2): return
        try:
            self.status_log.append("正在读取列名...")
            cols1 = pd.read_excel(path1, nrows=0).columns.tolist()
            cols2 = pd.read_excel(path2, nrows=0).columns.tolist()
            # Key for validation tab: UNION of all columns
            all_key_candidates = sorted(set(cols1) | set(cols2))
            self.key_column_combo.blockSignals(True)
            self.key_column_combo.clear()
            self.key_column_combo.addItems(all_key_candidates)
            self.key_column_combo.blockSignals(False)
            
            # Key for diff tab: INTERSECTION of columns
            common_cols = sorted(list(set(cols1) & set(cols2)))
            self.diff_key_combo.blockSignals(True)
            self.diff_key_combo.clear()
            self.diff_key_combo.addItems(common_cols)
            self.diff_key_combo.blockSignals(False)

            # 提取tab的基准列和列多选
            self._extract_cols1 = cols1
            self._extract_cols2 = cols2
            self.update_extract_key_combo()
            self.update_extract_cols_list()
            # txt转换tab的列选择
            self._txt_cols1 = cols1
            self._txt_cols2 = cols2
            self.update_txt_columns()
            # 其它tab下A/B表的列名都与实际表格一致
            self._populate_list_widget(self.unique1_cols_list, cols1, select_all=True)
            self._populate_list_widget(self.unique2_cols_list, cols2, select_all=True)
            self._populate_list_widget(self.validate_cols_list1, cols1, select_all=True)
            self._populate_list_widget(self.validate_cols_list2, cols2, select_all=True)
            self.update_sub_lists()
            self.status_log.append("列名更新完毕。")
        except Exception as e:
            QMessageBox.warning(self, "错误", f"读取文件列名失败: {e}")

    def update_extract_key_combo(self):
        # 根据基准值来源自动更新基准列下拉框
        base_file = self.extract_base_file_combo.currentText() if hasattr(self, 'extract_base_file_combo') else "文件1"
        cols = self._extract_cols1 if base_file == "文件1" else self._extract_cols2
        self.extract_key_combo.blockSignals(True)
        self.extract_key_combo.clear()
        self.extract_key_combo.addItems(cols)
        self.extract_key_combo.blockSignals(False)

    def update_extract_cols_list(self):
        # 根据提取目标文件自动更新提取列多选
        target_file = self.extract_target_file_combo.currentText() if hasattr(self, 'extract_target_file_combo') else "文件1"
        cols = self._extract_cols1 if target_file == "文件1" else self._extract_cols2
        self._populate_list_widget(self.extract_cols_list, cols, select_all=True)

    def update_txt_columns(self):
        # 根据选择的文件更新txt转换的列选择
        if not hasattr(self, '_txt_cols1') or not hasattr(self, '_txt_cols2'):
            return
        selected_file = self.txt_file_combo.currentText() if hasattr(self, 'txt_file_combo') else "文件1"
        cols = self._txt_cols1 if selected_file == "文件1" else self._txt_cols2

        # 更新两个下拉框
        self.txt_col1_combo.blockSignals(True)
        self.txt_col1_combo.clear()
        self.txt_col1_combo.addItems(cols)
        self.txt_col1_combo.blockSignals(False)

        self.txt_col2_combo.blockSignals(True)
        self.txt_col2_combo.clear()
        self.txt_col2_combo.addItems(cols)
        self.txt_col2_combo.blockSignals(False)

    def update_sub_lists(self):
        key_col = self.key_column_combo.currentText()
        # 文件1所有列
        if self.unique1_cols_list.count() > 0:
            all_cols1 = [self.unique1_cols_list.item(i).text() for i in range(self.unique1_cols_list.count())]
        else:
            all_cols1 = []
        # 文件2所有列
        if self.unique2_cols_list.count() > 0:
            all_cols2 = [self.unique2_cols_list.item(i).text() for i in range(self.unique2_cols_list.count())]
        else:
            all_cols2 = []
        # 去掉主键
        candidates1 = [c for c in all_cols1 if c != key_col]
        candidates2 = [c for c in all_cols2 if c != key_col]
        self._populate_list_widget(self.validate_cols_list1, candidates1, select_all=True)
        self._populate_list_widget(self.validate_cols_list2, candidates2, select_all=True)

    def _populate_list_widget(self, list_widget, items, select_all=False):
        list_widget.clear()
        for item_text in items:
            item = QListWidgetItem(item_text)
            item.setCheckState(Qt.CheckState.Checked if select_all else Qt.CheckState.Unchecked)
            list_widget.addItem(item)
    
    def _get_checked_items_in_order(self, list_widget):
        # 保持顺序返回被选中的列名
        return [list_widget.item(i).text() for i in range(list_widget.count()) if list_widget.item(i).checkState() == Qt.CheckState.Checked]

    def run_diff_comparison(self):
        file1, file2 = self.file1_path.text(), self.file2_path.text()
        key_column = self.diff_key_combo.currentText()
        if not all([file1, file2, key_column]):
            QMessageBox.warning(self, "提示", "请确保已选择两个文件并指定了对比基准列。")
            return
        unique1_cols = self._get_checked_items_in_order(self.unique1_cols_list)
        unique2_cols = self._get_checked_items_in_order(self.unique2_cols_list)
        output_path, _ = QFileDialog.getSaveFileName(self, "保存整体差异结果", "整体差异对比结果.xlsx", "Excel Files (*.xlsx)")
        if not output_path: return
        self.run_diff_button.setEnabled(False)
        self.status_log.clear()
        self.worker = ComparisonWorker(file1, file2, key_column, unique1_cols, unique2_cols)
        self.worker.finished.connect(lambda result: self.on_diff_finished(result, output_path))
        self.worker.progress.connect(self.progress_bar.setValue)
        self.worker.status.connect(self.log_status)
        self.worker.start()

    def on_diff_finished(self, result, path):
        self.run_diff_button.setEnabled(True)
        self.progress_bar.setValue(0)
        if result is None:
            QMessageBox.critical(self, "失败", "处理过程中发生错误，请查看日志。")
            return
        try:
            with pd.ExcelWriter(path) as writer:
                result["duplicates"].to_excel(writer, sheet_name=f"重复_{self.diff_key_combo.currentText()}", index=False)
                result["unique1"].to_excel(writer, sheet_name=f"文件1独有数据", index=False)
                result["unique2"].to_excel(writer, sheet_name=f"文件2独有数据", index=False)
            QMessageBox.information(self, "成功", f"对比完成！结果已保存到 {path}")
        except Exception as e:
            QMessageBox.critical(self, "保存失败", f"写入Excel文件失败: {e}")

    def run_validation_comparison(self):
        file1, file2 = self.file1_path.text(), self.file2_path.text()
        key_column = self.key_column_combo.currentText()
        if not all([file1, file2, key_column]):
            QMessageBox.warning(self, "提示", "请确保已选择两个文件并指定了行数据差异基准。")
            return
        cols1 = self._get_checked_items_in_order(self.validate_cols_list1)
        cols2 = self._get_checked_items_in_order(self.validate_cols_list2)
        if not cols1 or not cols2:
            QMessageBox.warning(self, "提示", "请在两个文件中都至少选择一列进行数据对比。")
            return
        if len(cols1) != len(cols2):
            QMessageBox.warning(self, "提示", "两个文件选择的对比列数量必须一致，且顺序一一对应！")
            return
        output_path, _ = QFileDialog.getSaveFileName(self, "保存数据差异结果", "数据差异对比结果.xlsx", "Excel Files (*.xlsx)")
        if not output_path: return
        self.run_validation_button.setEnabled(False)
        self.status_log.clear()
        self.val_worker = DataValidationWorker(file1, file2, key_column, cols1, cols2)
        self.val_worker.finished.connect(lambda result: self.on_validation_finished(result, output_path))
        self.val_worker.progress.connect(self.progress_bar.setValue)
        self.val_worker.status.connect(self.log_status)
        self.val_worker.start()
        
    def on_validation_finished(self, result, path):
        self.run_validation_button.setEnabled(True)
        self.progress_bar.setValue(0)
        if result is None:
            QMessageBox.critical(self, "失败", "处理过程中发生错误，请查看日志。")
            return
        try:
            result.to_excel(path, index=False)
            if result.empty:
                QMessageBox.information(self, "成功", f"对比完成！在共同行中，您选择对比的列均无差异。")
            else:
                QMessageBox.information(self, "成功", f"对比完成！已将发现的 {len(result)} 条差异保存到 {path}")
        except Exception as e:
            QMessageBox.critical(self, "保存失败", f"写入Excel文件失败: {e}")

    def run_extract(self):
        path1 = self.file1_path.text()
        path2 = self.file2_path.text()
        if not (path1 and path2):
            QMessageBox.warning(self, "提示", "请先选择两个文件。")
            return
        key_col = self.extract_key_combo.currentText()
        base_file = self.extract_base_file_combo.currentText()
        target_file = self.extract_target_file_combo.currentText()
        extract_cols = [self.extract_cols_list.item(i).text() for i in range(self.extract_cols_list.count()) if self.extract_cols_list.item(i).checkState() == Qt.CheckState.Checked]
        if not key_col or not extract_cols:
            QMessageBox.warning(self, "提示", "请先选择基准列和要提取的列。")
            return
        output_path, _ = QFileDialog.getSaveFileName(self, "保存提取结果", "提取结果.xlsx", "Excel Files (*.xlsx)")
        if not output_path:
            return
        try:
            df1 = pd.read_excel(path1)
            df2 = pd.read_excel(path2)
            # 容错：只保留第一个同名列
            if df1.columns.duplicated().any():
                df1 = df1.loc[:, ~df1.columns.duplicated()]
            if df2.columns.duplicated().any():
                df2 = df2.loc[:, ~df2.columns.duplicated()]
            base_df = df1 if base_file == "文件1" else df2
            target_df = df1 if target_file == "文件1" else df2
            
            # Clean key columns
            base_df[key_col] = base_df[key_col].astype(str).str.strip()
            target_df[key_col] = target_df[key_col].astype(str).str.strip()

            base_keys = set(base_df[key_col].dropna())
            target_keys = set(target_df[key_col].dropna())

            found_keys = base_keys.intersection(target_keys)
            not_found_keys = base_keys.difference(target_keys)

            # --- Build "Found" DataFrame ---
            if found_keys:
                # 保证提取列不含主键
                extract_cols_no_key = [col for col in extract_cols if col != key_col]
                
                result_found = target_df[target_df[key_col].isin(found_keys)][[key_col] + extract_cols_no_key]

                # 合并基准值来源文件的剩余列
                base_extra_cols = [col for col in base_df.columns if col != key_col and col not in extract_cols_no_key]
                if base_extra_cols:
                    base_info = base_df[[key_col] + base_extra_cols].drop_duplicates(subset=[key_col])
                    result_found = result_found.merge(base_info, on=key_col, how='left')
            else:
                result_found = pd.DataFrame()

            # --- Build "Not Found" DataFrame ---
            if not_found_keys:
                result_not_found = base_df[base_df[key_col].isin(not_found_keys)]
            else:
                result_not_found = pd.DataFrame()

            # --- Write to Excel ---
            with pd.ExcelWriter(output_path) as writer:
                result_found.to_excel(writer, sheet_name="提取成功", index=False)
                result_not_found.to_excel(writer, sheet_name="未在目标文件中找到的基准行", index=False)

            QMessageBox.information(self, "成功", f"提取完成！\n提取成功 {len(result_found)} 条。\n未找到 {len(result_not_found)} 条。\n结果已保存到 {output_path}")

        except Exception as e:
            QMessageBox.critical(self, "失败", f"提取失败: {e}")

    def select_merge_files(self):
        files, _ = QFileDialog.getOpenFileNames(self, "选择要合并的Excel文件", "", "Excel Files (*.xlsx *.xls)")
        if files:
            self.merge_files_edit.setText(";".join(files))

    def run_merge(self):
        files = self.merge_files_edit.text().split(";") if self.merge_files_edit.text() else []
        files = [f for f in files if f.strip()]
        if not files or len(files) < 2:
            QMessageBox.warning(self, "提示", "请至少选择两个要合并的Excel文件。")
            return
        output_path, _ = QFileDialog.getSaveFileName(self, "保存合并结果", "合并结果.xlsx", "Excel Files (*.xlsx)")
        if not output_path:
            return
        try:
            dfs = []
            for f in files:
                df = pd.read_excel(f)
                df["来源文件"] = f.split("/")[-1] if "/" in f else f.split("\\")[-1]
                dfs.append(df)
            merged = pd.concat(dfs, ignore_index=True, sort=False)
            merged.to_excel(output_path, index=False)
            QMessageBox.information(self, "成功", f"合并完成！共合并 {len(files)} 个文件，{len(merged)} 行。结果已保存到 {output_path}")
        except Exception as e:
            QMessageBox.critical(self, "失败", f"合并失败: {e}")

    def run_column_to_txt(self):
        # 获取选择的文件路径
        selected_file = self.txt_file_combo.currentText()
        file_path = self.file1_path.text() if selected_file == "文件1" else self.file2_path.text()

        if not file_path:
            QMessageBox.warning(self, "提示", f"请先选择{selected_file}。")
            return

        # 获取选择的列
        col1 = self.txt_col1_combo.currentText()
        col2 = self.txt_col2_combo.currentText()

        if not col1 or not col2:
            QMessageBox.warning(self, "提示", "请选择两列数据。")
            return

        if col1 == col2:
            QMessageBox.warning(self, "提示", "请选择不同的两列。")
            return

        # 选择保存路径
        output_path, _ = QFileDialog.getSaveFileName(self, "保存txt文件", "列数据.txt", "Text Files (*.txt)")
        if not output_path:
            return

        try:
            # 读取Excel文件
            self.status_log.append(f"正在读取{selected_file}...")
            df = pd.read_excel(file_path)

            # 检查列是否存在
            if col1 not in df.columns:
                QMessageBox.critical(self, "错误", f"列 '{col1}' 不存在于{selected_file}中。")
                return
            if col2 not in df.columns:
                QMessageBox.critical(self, "错误", f"列 '{col2}' 不存在于{selected_file}中。")
                return

            # 提取两列数据并处理空值
            data1 = df[col1].fillna('').astype(str)
            data2 = df[col2].fillna('').astype(str)

            # 生成txt内容
            txt_lines = []
            for i in range(len(data1)):
                line = f"{data1.iloc[i]}----{data2.iloc[i]}"
                txt_lines.append(line)

            # 写入txt文件
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(txt_lines))

            self.status_log.append(f"成功生成txt文件，共{len(txt_lines)}行数据。")
            QMessageBox.information(self, "成功", f"txt文件生成完成！\n共处理 {len(txt_lines)} 行数据。\n文件已保存到: {output_path}")

        except Exception as e:
            QMessageBox.critical(self, "失败", f"生成txt文件失败: {e}")
            self.status_log.append(f"生成txt文件失败: {e}")

    def log_status(self, message):
        self.status_log.append(message)

    def reset_all(self):
        # 清空文件路径
        self.file1_path.clear()
        self.file2_path.clear()
        # 清空所有下拉框
        self.diff_key_combo.clear()
        self.key_column_combo.clear()
        self.extract_key_combo.clear()
        if hasattr(self, 'extract_base_file_combo'):
            self.extract_base_file_combo.setCurrentIndex(0)
        if hasattr(self, 'extract_target_file_combo'):
            self.extract_target_file_combo.setCurrentIndex(0)
        # 清空txt转换相关控件
        if hasattr(self, 'txt_file_combo'):
            self.txt_file_combo.setCurrentIndex(0)
        if hasattr(self, 'txt_col1_combo'):
            self.txt_col1_combo.clear()
        if hasattr(self, 'txt_col2_combo'):
            self.txt_col2_combo.clear()
        # 清空所有多选列表
        self.unique1_cols_list.clear()
        self.unique2_cols_list.clear()
        self.validate_cols_list1.clear()
        self.validate_cols_list2.clear()
        self.extract_cols_list.clear()
        # 清空合并文件输入
        if hasattr(self, 'merge_files_edit'):
            self.merge_files_edit.clear()
        # 清空进度条和日志
        self.progress_bar.setValue(0)
        self.status_log.clear()


if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec()) 