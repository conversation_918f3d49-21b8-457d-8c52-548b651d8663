import pandas as pd
import os

def compare_waybills(file1, file2, column_name):
    """
    比较两个Excel文件中的运单号，并输出重复项和不重复项到新的Excel文件中。
    对于独有运单号，将输出该行的所有列数据。

    :param file1: 第一个Excel文件名 (例如 '总.xlsx')
    :param file2: 第二个Excel文件名 (例如 '对比.xlsx')
    :param column_name: 运单号所在的列名 (例如 '运单号')
    """
    try:
        # 读取Excel文件
        df1 = pd.read_excel(file1, dtype={column_name: str})
        df2 = pd.read_excel(file2, dtype={column_name: str})

        # 清理运单号列（去除空格）
        df1[column_name] = df1[column_name].astype(str).str.strip()
        df2[column_name] = df2[column_name].astype(str).str.strip()

        # 获取运单号集合
        waybills1 = set(df1[column_name].dropna())
        waybills2 = set(df2[column_name].dropna())

        # 找出重复的运单号
        duplicate_waybills = list(waybills1.intersection(waybills2))

        # 找出不重复的运单号对应的完整数据行
        unique_to_file1_df = df1[~df1[column_name].isin(waybills2)]
        unique_to_file2_df = df2[~df2[column_name].isin(waybills1)]

        # 创建一个Excel writer
        output_filename = '运单号对比结果.xlsx'
        with pd.ExcelWriter(output_filename) as writer:
            # 将重复运单号写入第一个sheet
            pd.DataFrame(duplicate_waybills, columns=['重复运单号']).to_excel(writer, sheet_name='重复运单号', index=False)
            
            # 将独有运单号的完整数据行写入相应的sheet
            unique_to_file1_df.to_excel(writer, sheet_name='总表独有数据', index=False)
            unique_to_file2_df.to_excel(writer, sheet_name='对比表独有数据', index=False)

        print(f"对比完成，结果已保存到 '{output_filename}'")
        print(f"重复的运单号数量: {len(duplicate_waybills)}")
        print(f"只在 '{file1}' 中存在的运单号数量: {len(unique_to_file1_df)}")
        print(f"只在 '{file2}' 中存在的运单号数量: {len(unique_to_file2_df)}")

    except FileNotFoundError as e:
        print(f"错误: 找不到文件 {e.filename}。请确保文件在当前目录下。")
    except KeyError:
        print(f"错误: 列 '{column_name}' 在某个文件中不存在。请检查列名是否正确。")
    except Exception as e:
        print(f"发生未知错误: {e}")

if __name__ == '__main__':
    # --- 请在这里配置您的文件和列名 ---
    file1 = '总.xlsx'
    file2 = '对比.xlsx'
    column_name = '运单号'
    # ------------------------------------

    compare_waybills(file1, file2, column_name) 